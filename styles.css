/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

/* Container and layout */
.container {
  display: flex;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Sidebar styles */
.sidebar {
  width: 320px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.profile-section {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.avatar-container {
  margin-bottom: 1rem;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
  background-color: rgba(255, 255, 255, 0.1);
}

.name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.title {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Contact section */
.contact-section {
  margin-bottom: 2rem;
}

.contact-section h2 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
  font-size: 0.9rem;
}

.contact-item a {
  color: white;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

/* Icon placeholders */
.icon-placeholder {
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin-right: 0.8rem;
  flex-shrink: 0;
  position: relative;
}

.icon-placeholder::after {
  content: attr(data-icon);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 8px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
}

/* Links section */
.links-section h2 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.social-link {
  display: flex;
  align-items: center;
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.5rem;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Main content */
.main-content {
  flex: 1;
  padding: 3rem;
  overflow-y: auto;
}

.main-content section {
  margin-bottom: 3rem;
}

.main-content h2 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
  position: relative;
}

.main-content h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.container {
  display: flex;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 左侧边栏 */
.sidebar {
  width: 300px;
  min-height: 100vh;
  background: #fff;
  padding: 30px 20px;
  border-right: 1px solid #eee;
  position: sticky;
  top: 0;
}

.profile {
  text-align: center;
  margin-bottom: 30px;
}

.avatar-container {
  margin-bottom: 20px;
}

.avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #e9ecef;
}

.name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #2c3e50;
}

.title {
  color: #666;
  font-size: 16px;
}

.basic-info,
.external-links {
  margin-bottom: 30px;
}

.info-item,
.link-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 0;
}

.info-icon,
.link-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.info-text {
  color: #555;
  font-size: 14px;
}

.link-item {
  transition: background-color 0.3s;
  border-radius: 8px;
  padding: 10px;
  margin: 5px -10px;
}

.link-item:hover {
  background-color: #f8f9fa;
}

.link-item a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
}

/* 右侧内容区域 */
.content {
  flex: 1;
  padding: 30px 40px;
  overflow-y: auto;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 22px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.intro-content {
  font-size: 16px;
  line-height: 1.8;
  color: #444;
}

/* 新闻列表 */
.news-item {
  margin-bottom: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.news-date {
  color: #666;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}

.news-content {
  color: #555;
  font-size: 15px;
}

/* 时间线样式 */
.timeline-item {
  display: flex;
  margin-bottom: 25px;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.timeline-icon {
  width: 60px;
  height: 60px;
  margin-right: 20px;
  flex-shrink: 0;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e9ecef;
}

.timeline-icon img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.timeline-org {
  color: #666;
  font-size: 16px;
  margin-bottom: 5px;
}

.timeline-date {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
}

.timeline-desc {
  color: #555;
  font-size: 15px;
  line-height: 1.6;
}

/* 出版物列表 */
.publication-item {
  margin-bottom: 25px;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.publication-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.publication-title a {
  color: #2c3e50;
  text-decoration: none;
}

.publication-title a:hover {
  color: #3498db;
}

.publication-authors {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.publication-venue {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
  font-style: italic;
}

.publication-desc {
  color: #555;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    min-height: auto;
    position: static;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding: 20px;
  }

  .avatar {
    width: 120px;
    height: 120px;
  }

  .content {
    padding: 20px;
  }

  .timeline-item {
    flex-direction: column;
    text-align: center;
  }

  .timeline-icon {
    margin: 0 auto 15px auto;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 15px;
  }

  .section h2 {
    font-size: 20px;
  }

  .avatar {
    width: 100px;
    height: 100px;
  }

  .name {
    font-size: 20px;
  }
}
